import { ThemeColorKeys } from '@/types/color-types';
import { Pressable, Text, View } from '@components/native';
import { useTheme } from '@context/index';
import { BottomSheetModal, BottomSheetModalProps, BottomSheetView } from '@gorhom/bottom-sheet';
import React, { FC, ReactNode, memo, useCallback, useMemo, useRef } from 'react';
import { Keyboard } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';

type UseBottomSheetOptions = {
  snapPoints?: (string | number)[];
  onDismiss?: () => void;
};

interface SheetProps<T> extends BottomSheetModalProps {
  title?: string;
  wrapWithSheetView?: boolean;
  bg?: ThemeColorKeys;
  withBackButton?: boolean;
}

export function useBottomSheet(options?: UseBottomSheetOptions) {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const { colors, theme, sheetValue } = useTheme();
  const { top } = useSafeAreaInsets();
  const snapPoints = useMemo(() => options?.snapPoints ?? [], [options?.snapPoints]);

  const onDismiss = useMemo(() => options?.onDismiss, [options?.onDismiss]);

  // Memoize handleSheetChanges to ensure stability
  const handleSheetChanges = useCallback(
    (index: number) => {
      console.log('Current Bottom Sheet index >> ', index);
      // console.log('BottomSheet onChange, index:', index, 'isOpen:', index > -1);
      sheetValue.set(index > -1);
    },
    [sheetValue],
  );

  const openSheet = useCallback(() => {
    bottomSheetRef.current?.present();
    Keyboard.dismiss();
  }, []);

  const closeSheet = useCallback(() => {
    bottomSheetRef.current?.dismiss();
  }, []);

  // Memoized Sheet component without dependency array
  const Sheet: FC<SheetProps<any>> = memo(({ title, withBackButton = false, bg = 'background', wrapWithSheetView = false, ...props }) => {
    let baseSheetContent = props.children as ReactNode | ReactNode[];

    if (wrapWithSheetView) {
      baseSheetContent = (
        <BottomSheetView
          style={{
            borderTopRightRadius: 20,
            borderTopLeftRadius: 20,
          }}>
          {baseSheetContent}
        </BottomSheetView>
      );
    }

    return (
      <BottomSheetModal
        ref={bottomSheetRef}
        onChange={handleSheetChanges}
        snapPoints={snapPoints}
        onDismiss={onDismiss}
        handleIndicatorStyle={{
          backgroundColor: colors.neutral40,
        }}
        topInset={top}
        handleComponent={() => (
          <View display="flex" ai="center" pos="relative" fd="row" jc="center">
            {withBackButton && (
              <View pos="absolute" top="50%" left={15}>
                <Pressable onPress={closeSheet}>
                  <Ionicons name="arrow-back-outline" color={colors.neutral70} size={22} />
                </Pressable>
              </View>
            )}
            <View bg={bg} flexCenterColumn btl={24} btr={24}>
              <View br={50} w={40} mt={10} mb={5} bg="neutral50" h={4} />
              {title && (
                <Text mx={10} fw="700" fs="14" my={5} ta="center">
                  {title}
                </Text>
              )}
            </View>
          </View>
        )}
        backgroundStyle={{ backgroundColor: theme === 'dark' ? '#1b1b1b' : bg }}
        style={{
          borderTopRightRadius: 20,
          borderTopLeftRadius: 20,
          zIndex: 20,
        }}
        {...props}>
        {baseSheetContent}
      </BottomSheetModal>
    );
  });

  // Ensure Sheet has a display name for debugging
  Sheet.displayName = 'BottomSheet';

  // Memoize the hook's return value
  return useMemo(
    () => ({
      openSheet,
      closeSheet,
      Sheet,
    }),
    [openSheet, closeSheet, Sheet],
  );
}
