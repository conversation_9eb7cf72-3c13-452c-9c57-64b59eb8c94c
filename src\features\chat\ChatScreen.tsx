import ChatHealBadgeIcon from '@assets/svgs/chat-heal-badge.svg';
import HandWave from '@assets/svgs/hand-wavesvg.svg';
import { Button, Text, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import { BounceTap } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { MotiView } from 'moti';
import { useState } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { Easing } from 'react-native-reanimated';
export const ChatScreen = () => {
  const { openSheet, Sheet, closeSheet } = useBottomSheet();
  return (
    <>
      <ScreenWrapper
        hideBackButton={false}
        title="@sunshine"
        headerStyles={styles.headerRight}
        customHeaderComponent={
          <View>
            <BounceTap onPress={openSheet}>
              <ChatHealBadgeIcon />
            </BounceTap>
          </View>
        }>
        <View flex={1} gap={40} display="flex" fd="column" ai="center" mt={50}>
          <Text fw="500" color="neutral80">
            Say Hi! and start a conversation.
          </Text>
          <MotiView
            from={{
              rotate: '0deg', // Starting rotation
              translateY: 0, // Starting position
            }}
            animate={{
              rotate: ['0deg', '35deg', '0deg', '-15deg', '0deg'], // Rotate left, then right, mimicking wave
              translateY: [0, 5, 0, 5, 0],
            }}
            transition={{
              type: 'timing',
              duration: 200, // Duration of each segment
              repeatReverse: false, // Cycle through the sequence
              easing: Easing.linear, // Smooth transitions
            }}>
            <HandWave />
          </MotiView>
        </View>
      </ScreenWrapper>
      <Sheet wrapWithSheetView>
        <ChatInteractionSheetContent closeSheet={closeSheet} userId="123" username="subshine" />
      </Sheet>
    </>
  );
};

type ChatInteractionSheetContent = {
  userId: string;
  username: string;
  closeSheet: () => void;
};
const ChatInteractionSheetContent: React.FC<ChatInteractionSheetContent> = ({ userId, username, closeSheet }) => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  return (
    <View display="flex" fd="column" ai="center" pb={40} px={20} pt={16}>
      <Avatar withBounceTap={false} />
      <Text color="neutral80" fw="500" ta="center" my={10}>
        Has this interaction with <Text fw="700">{username}</Text> helped you?
      </Text>
      <Text fs="12" ta="center" fw="500" color="neutral60" my={20}>
        Give a badge to motivate them to help others too.
      </Text>

      <View mt={20} disableSizeMatter display="flex" gap={20} ai="center" fd="row" jc="space-between">
        {Array.from({ length: 4 }).map((_, i) => (
          <Pressable style={{ flex: 1 }} onPress={() => setSelectedIndex(i)} key={i}>
            <View flexCenterColumn br={16} bg={selectedIndex == i ? 'purple200' : 'neutral20'} h={80} flex={1}>
              <View bg="negative30" size={50}></View>
              <Text mx={8} ta="center" fw={selectedIndex == i ? '600' : '500'} fs="12">
                Soul Listener
              </Text>
            </View>
          </Pressable>
        ))}
      </View>
      <View mt={80} display="flex" fd="row" gap={8} ai="center">
        <Button onPress={closeSheet} h={46} bg="background" bc="purple500" bw={1} color="purple500" isHorizontalAligned>
          Cancel
        </Button>
        <Button onPress={() => {}} h={46} isHorizontalAligned>
          Submit
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerRight: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: 20,
  },
});
