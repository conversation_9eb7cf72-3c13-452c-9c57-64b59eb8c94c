import { Button, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { notify } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { Controller, useForm } from 'react-hook-form';
import { KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { Checkbox } from './components/Checkbox';

export const CreatePoll = () => {
  const { control, setValue, getValues } = useForm({
    defaultValues: {
      question: '',
      options: 2,
      option1: '',
      option2: '',
      option3: '',
      option4: '',
      option5: '',
      multiSelect: false,
      disableComments: false,
    },
  });

  const { isMutating, mutateAsync } = useStitch('createPoll', {
    mutationOptions: {
      onSuccess: async () => {
        notify.bottom('Poll created successfully!');
        router.back();
      },
      onError: error => {
        console.error('Create poll error:', error);
      },
    },
  });

  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          New Poll
        </Text>
      }
      px={20}>
      <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} style={{ flex: 1 }}>
        <ScrollView>
          <View pt={16} flex={1}>
            <Text fw="400" fs="12" color="purple500" ml={9} mb={4}>
              Question
            </Text>
            {/* Add minLength 30 */}
            <TextInput label="Ask a question" labelType="background" maxLength={1000} control={control} name="question" gapBottom={4} />
            <Text fw="400" fs="10" color="neutral50" ml={9} mb={24}>
              25 maximum characters
            </Text>
            <View fd="row" ai="center" jc="space-between" ml={9} mb={4}>
              <Text fw="400" fs="12" color="purple500">
                Options
              </Text>
              <View fd="row" ai="center">
                <Controller
                  control={control}
                  name="multiSelect"
                  render={({ field: { value, onChange } }) => (
                    <Checkbox
                      checked={value}
                      onChange={newValue => {
                        console.log('newValue', newValue);
                        onChange(newValue);
                        setValue('multiSelect', newValue);
                      }}
                    />
                  )}
                />
                <Text fw="400" fs="12" color="neutral70" ml={4}>
                  multiSelect
                </Text>
              </View>
            </View>
            <Controller
              control={control}
              name="options"
              render={({ field: { value, onChange } }) => (
                <>
                  {[...Array(value)].map((_, index) => (
                    //  Add minLength 2
                    <TextInput key={index} label={`Option ${index + 1}`} labelType="background" maxLength={50} control={control} name={`option${index + 1}`} gapBottom={12} />
                  ))}
                  {value < 5 && (
                    <Button bw={1} bc="purple500" bg="transparent" color="purple700" isFullWidth={true} onPress={() => onChange(value + 1)} disabled={value >= 5}>
                      + ADD OPTION
                    </Button>
                  )}
                </>
              )}
            />
            <View fd="row" ai="center" jc="space-between" pt={41}>
              <Text fw="400" fs="12" color="neutral70">
                Disable Comments
              </Text>
              <Controller
                control={control}
                name="disableComments"
                render={({ field: { value, onChange } }) => (
                  <Switch
                    isActive={value}
                    onToggle={newValue => {
                      onChange(newValue);
                      setValue('disableComments', newValue);
                    }}
                  />
                )}
              />
            </View>
          </View>
        </ScrollView>
        <View pos="absolute" bottom={53} left={0} right={0}>
          <Button
            isFullWidth={true}
            isLoading={isMutating}
            onPress={() =>
              mutateAsync({
                title: getValues('question'),
                options: [getValues('option1'), getValues('option2'), getValues('option3'), getValues('option4'), getValues('option5')].filter(
                  (option): option is string => typeof option === 'string' && option.trim() !== '',
                ),
                multiSelect: getValues('multiSelect'),
                disableComments: getValues('disableComments'),
              })
            }>
            ADD
          </Button>
        </View>
      </KeyboardAvoidingView>
    </ScreenWrapper>
  );
};
