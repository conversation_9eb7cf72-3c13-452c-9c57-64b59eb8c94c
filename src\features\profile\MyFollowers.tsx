import { Button, Text, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import { FlashList } from '@shopify/flash-list';
import React from 'react';

const data = Array.from({ length: 50 }).fill({
  id: 'user1id',
  username: 'S0123',
  mood: 'Feeling Optimistic',
  avatar: 'avatarUrl',
});

export const MyFollowers = () => {
  return (
    <ScreenWrapper title="My Followers">
      <View flex={1}>
        <FlashList
          onRefresh={() => {}}
          refreshing={false}
          estimatedItemSize={data.length}
          data={data}
          contentContainerStyle={{ paddingVertical: 20 }}
          renderItem={({ item, index }) => {
            return (
              <View key={index} p={10} display="flex" fd="row" ai="center" jc="space-between">
                <View display="flex" fd="row" gap={7} ai="center">
                  <Avatar size={50} />
                  <View gap={5} px={6}>
                    <Text color="neutral80" numberOfLines={1} fw="600" fs="12">
                      {item.username} {index}
                    </Text>
                    <Text numberOfLines={1} color="neutral40" fs="12">
                      {item.mood}
                    </Text>
                  </View>
                </View>
                <Button br={32} px={24} onPress={() => {}} h={32}>
                  <Text fw="500" fs="12" color="white">
                    Chat
                  </Text>
                </Button>
              </View>
            );
          }}
        />
      </View>
    </ScreenWrapper>
  );
};
